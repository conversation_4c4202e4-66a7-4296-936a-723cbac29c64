#!/usr/bin/env tsx

/**
 * 内存压力测试脚本
 * 
 * 专门测试并发聊天请求后的内存占用情况
 */

import { performance } from 'perf_hooks';

interface TestConfig {
    baseUrl: string;
    concurrentUsers: number;
    requestsPerUser: number;
    delayBetweenRequests: number;
    memoryCheckInterval: number;
}

interface MemorySnapshot {
    timestamp: number;
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers: number;
}

class MemoryStressTest {
    private readonly config: TestConfig;
    private readonly memorySnapshots: MemorySnapshot[] = [];
    private monitoringInterval: NodeJS.Timeout | null = null;

    constructor(config: TestConfig) {
        this.config = config;
    }

    /**
     * 开始内存监控
     */
    private startMemoryMonitoring(): void {
        console.log('🔍 Starting memory monitoring...');
        
        this.monitoringInterval = setInterval(() => {
            const mem = process.memoryUsage();
            const snapshot: MemorySnapshot = {
                timestamp: Date.now(),
                rss: Math.round(mem.rss / 1024 / 1024),
                heapTotal: Math.round(mem.heapTotal / 1024 / 1024),
                heapUsed: Math.round(mem.heapUsed / 1024 / 1024),
                external: Math.round(mem.external / 1024 / 1024),
                arrayBuffers: Math.round(mem.arrayBuffers / 1024 / 1024),
            };
            
            this.memorySnapshots.push(snapshot);
            
            console.log(
                `📊 Memory: RSS=${snapshot.rss}MB, ` +
                `Heap=${snapshot.heapUsed}/${snapshot.heapTotal}MB, ` +
                `External=${snapshot.external}MB`
            );
        }, this.config.memoryCheckInterval);
    }

    /**
     * 停止内存监控
     */
    private stopMemoryMonitoring(): void {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
    }

    /**
     * 创建聊天会话
     */
    private async createSession(userId: string, agentId: string): Promise<string> {
        const url = `${this.config.baseUrl}/api/session/create?agentId=${agentId}&externalId=test-${userId}-${Date.now()}`;
        const response = await globalThis.fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'userId': userId,
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to create session: ${response.status}`);
        }

        const data = await response.json() as { data: { id: string } };
        return data.data.id;
    }

    /**
     * 发送聊天消息
     */
    private async sendChatMessage(sessionId: string, userId: string, messageContent: string): Promise<void> {
        const response = await globalThis.fetch(`${this.config.baseUrl}/api/session/${sessionId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'userId': userId,
                'Accept': 'text/event-stream',
            },
            body: JSON.stringify({
                agentId: '44584029-a411-4ec0-ba53-1350b064d56e',
                message: {
                    id: `msg-${Date.now()}-${Math.random()}`,
                    role: 'user',
                    content: [{
                        type: 'text',
                        text: messageContent
                    }],
                    sender: {
                        id: userId
                    }
                }
            })
        });

        if (!response.ok) {
            throw new Error(`Chat request failed: ${response.status}`);
        }

        // 读取SSE流
        const reader = response.body?.getReader();
        if (!reader) {
            throw new Error('No response body');
        }

        try {
            // eslint-disable-next-line no-constant-condition
            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    break;
                }

                // 解析SSE数据（简单处理）
                const decoder = new globalThis.TextDecoder();
                const chunk = decoder.decode(value);
                if (chunk.includes('session-finish')) {
                    break;
                }
            }
        } finally {
            reader.releaseLock();
        }
    }

    /**
     * 模拟单个用户的聊天行为
     */
    private async simulateUser(userId: string): Promise<void> {
        try {
            console.log(`👤 User ${userId} starting...`);
            
            // 创建会话
            const sessionId = await this.createSession(userId, '44584029-a411-4ec0-ba53-1350b064d56e');
            
            // 发送多条消息
            for (let i = 0; i < this.config.requestsPerUser; i++) {
                const message = `Test message ${i + 1} from user ${userId}. This is a longer message to test memory usage with more content.`;
                
                await this.sendChatMessage(sessionId, userId, message);
                
                if (i < this.config.requestsPerUser - 1) {
                    await this.delay(this.config.delayBetweenRequests);
                }
            }
            
            console.log(`✅ User ${userId} completed`);
        } catch (error) {
            console.error(`❌ User ${userId} failed:`, error);
        }
    }

    /**
     * 延迟函数
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 运行并发测试
     */
    public async runTest(): Promise<void> {
        console.log('🚀 Starting Memory Stress Test');
        console.log(`📋 Config: ${this.config.concurrentUsers} users, ${this.config.requestsPerUser} requests each`);
        console.log('=' .repeat(60));

        // 记录初始内存
        const initialMemory = process.memoryUsage();
        console.log(`📊 Initial Memory: RSS=${Math.round(initialMemory.rss / 1024 / 1024)}MB, Heap=${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`);

        // 开始内存监控
        this.startMemoryMonitoring();

        const startTime = performance.now();

        try {
            // 创建并发用户
            const userPromises = [];
            for (let i = 0; i < this.config.concurrentUsers; i++) {
                const userId = `test-user-${i.toString().padStart(3, '0')}`;
                userPromises.push(this.simulateUser(userId));
            }

            // 等待所有用户完成
            await Promise.allSettled(userPromises);

            const endTime = performance.now();
            const duration = Math.round(endTime - startTime);

            console.log('=' .repeat(60));
            console.log(`✅ Test completed in ${duration}ms`);

            // 等待一段时间观察内存变化
            console.log('⏳ Waiting 30 seconds to observe memory changes...');
            await this.delay(30000);

            // 强制垃圾回收（如果可用）
            if (global.gc) {
                console.log('🗑️  Forcing garbage collection...');
                global.gc();
                await this.delay(5000);
            }

            // 最终内存检查
            const finalMemory = process.memoryUsage();
            console.log(`📊 Final Memory: RSS=${Math.round(finalMemory.rss / 1024 / 1024)}MB, Heap=${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`);

            // 分析内存变化
            this.analyzeMemoryUsage(initialMemory, finalMemory);

        } finally {
            this.stopMemoryMonitoring();
        }
    }

    /**
     * 分析内存使用情况
     */
    private analyzeMemoryUsage(initial: NodeJS.MemoryUsage, final: NodeJS.MemoryUsage): void {
        console.log('\n📈 Memory Analysis:');
        console.log('=' .repeat(40));

        const rssDiff = Math.round((final.rss - initial.rss) / 1024 / 1024);
        const heapDiff = Math.round((final.heapUsed - initial.heapUsed) / 1024 / 1024);
        const externalDiff = Math.round((final.external - initial.external) / 1024 / 1024);

        console.log(`RSS Change: ${rssDiff > 0 ? '+' : ''}${rssDiff}MB`);
        console.log(`Heap Change: ${heapDiff > 0 ? '+' : ''}${heapDiff}MB`);
        console.log(`External Change: ${externalDiff > 0 ? '+' : ''}${externalDiff}MB`);

        // 内存泄漏警告
        if (heapDiff > 50) {
            console.log('⚠️  WARNING: Significant heap memory increase detected!');
        }
        if (rssDiff > 100) {
            console.log('⚠️  WARNING: Significant RSS memory increase detected!');
        }

        // 峰值内存
        const peakMemory = this.memorySnapshots.reduce((max, snapshot) => 
            snapshot.heapUsed > max.heapUsed ? snapshot : max
        );
        console.log(`📊 Peak Heap Usage: ${peakMemory.heapUsed}MB`);
    }
}

// 运行测试
async function main(): Promise<void> {
    const config: TestConfig = {
        baseUrl: 'http://localhost:3000',
        concurrentUsers: 50,           // 50个并发用户
        requestsPerUser: 2,            // 每个用户发送2条消息
        delayBetweenRequests: 1000,    // 消息间隔1秒
        memoryCheckInterval: 2000,     // 每2秒检查一次内存
    };

    const test = new MemoryStressTest(config);
    await test.runTest();
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}
