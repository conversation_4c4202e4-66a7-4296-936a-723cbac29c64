#!/usr/bin/env tsx

/**
 * 简化版内存测试脚本
 * 
 * 快速测试并发聊天请求的内存使用情况
 */

async function sendChatRequest(userId: string, sessionId: string): Promise<void> {
    const response = await globalThis.fetch(`http://localhost:3000/api/session/${sessionId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'userId': userId,
            'Accept': 'text/event-stream',
        },
        body: JSON.stringify({
            agentId: '44584029-a411-4ec0-ba53-1350b064d56e',
            message: {
                id: `msg-${Date.now()}-${Math.random()}`,
                role: 'user',
                content: [{
                    type: 'text',
                    text: `Test message from user ${userId}. This is a test message to check memory usage.`
                }],
                sender: {
                    id: userId
                }
            }
        })
    });

    if (!response.ok) {
        throw new Error(`Chat request failed: ${response.status}`);
    }

    // 读取SSE流
    const reader = response.body?.getReader();
    if (!reader) {
        throw new Error('No response body');
    }

    try {
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const { done, value } = await reader.read();
            if (done) {
                break;
            }

            const decoder = new globalThis.TextDecoder();
            const chunk = decoder.decode(value);
            if (chunk.includes('session-finish')) {
                break;
            }
        }
    } finally {
        reader.releaseLock();
    }
}

async function createSession(userId: string): Promise<string> {
    const url = `http://localhost:3000/api/session/create?agentId=44584029-a411-4ec0-ba53-1350b064d56e&externalId=test-${userId}-${Date.now()}`;
    const response = await globalThis.fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'userId': userId,
        }
    });

    if (!response.ok) {
        throw new Error(`Failed to create session: ${response.status}`);
    }

    const data = await response.json() as { data: { id: string } };
    return data.data.id;
}

function formatMemory(bytes: number): string {
    return `${Math.round(bytes / 1024 / 1024)}MB`;
}

function logMemory(label: string): void {
    const mem = process.memoryUsage();
    console.log(
        `${label}: RSS=${formatMemory(mem.rss)}, ` +
        `Heap=${formatMemory(mem.heapUsed)}/${formatMemory(mem.heapTotal)}, ` +
        `External=${formatMemory(mem.external)}`
    );
}

async function runMemoryTest(): Promise<void> {
    console.log('🚀 Starting Simple Memory Test');
    console.log('================================');

    // 初始内存
    logMemory('📊 Initial Memory');

    const concurrentUsers = 20;
    const requestsPerUser = 2;

    console.log(`\n📋 Test Config: ${concurrentUsers} users, ${requestsPerUser} requests each`);

    // 开始测试
    const startTime = Date.now();

    try {
        const userPromises = [];
        
        for (let i = 0; i < concurrentUsers; i++) {
            const userId = `test-user-${i.toString().padStart(3, '0')}`;
            
            const userTask = async (): Promise<void> => {
                try {
                    const sessionId = await createSession(userId);
                    
                    for (let j = 0; j < requestsPerUser; j++) {
                        await sendChatRequest(userId, sessionId);
                    }
                    
                    console.log(`✅ User ${userId} completed`);
                } catch (error) {
                    console.error(`❌ User ${userId} failed:`, error);
                }
            };
            
            userPromises.push(userTask());
        }

        // 等待所有用户完成
        await Promise.allSettled(userPromises);

        const duration = Date.now() - startTime;
        console.log(`\n✅ Test completed in ${duration}ms`);

        // 测试完成后的内存
        logMemory('📊 After Test Memory');

        // 等待30秒观察内存变化
        console.log('\n⏳ Waiting 30 seconds to observe memory changes...');
        
        let countdown = 30;
        const countdownInterval = setInterval(() => {
            logMemory(`📊 Memory (${countdown}s remaining)`);
            countdown--;
            
            if (countdown <= 0) {
                clearInterval(countdownInterval);
            }
        }, 5000);

        await new Promise(resolve => setTimeout(resolve, 30000));

        // 强制垃圾回收（如果可用）
        if (global.gc) {
            console.log('\n🗑️  Forcing garbage collection...');
            global.gc();
            await new Promise(resolve => setTimeout(resolve, 2000));
            logMemory('📊 After GC Memory');
        } else {
            console.log('\n💡 Tip: Run with --expose-gc flag to enable forced garbage collection');
        }

        // 最终内存
        logMemory('📊 Final Memory');

    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runMemoryTest().catch(console.error);
}
