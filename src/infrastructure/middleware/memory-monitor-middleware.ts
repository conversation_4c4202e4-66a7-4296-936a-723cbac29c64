/**
 * 内存监控中间件
 * 
 * 监控每个请求的内存使用情况，帮助识别内存泄漏
 */

import type { Request, Response, NextFunction } from 'express';
import { Logger } from '@/infrastructure/logger';

interface MemoryStats {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers: number;
}

/**
 * 内存监控中间件
 */
export function memoryMonitorMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
): void {
    const startTime = Date.now();
    const memBefore = process.memoryUsage();

    // 监听响应完成事件
    res.on('finish', () => {
        const endTime = Date.now();
        const memAfter = process.memoryUsage();
        const duration = endTime - startTime;

        // 计算内存差异
        const memDiff = {
            rss: memAfter.rss - memBefore.rss,
            heapTotal: memAfter.heapTotal - memBefore.heapTotal,
            heapUsed: memAfter.heapUsed - memBefore.heapUsed,
            external: memAfter.external - memBefore.external,
            arrayBuffers: memAfter.arrayBuffers - memBefore.arrayBuffers,
        };

        // 转换为MB
        const memDiffMB = {
            rss: Math.round(memDiff.rss / 1024 / 1024 * 100) / 100,
            heapTotal: Math.round(memDiff.heapTotal / 1024 / 1024 * 100) / 100,
            heapUsed: Math.round(memDiff.heapUsed / 1024 / 1024 * 100) / 100,
            external: Math.round(memDiff.external / 1024 / 1024 * 100) / 100,
            arrayBuffers: Math.round(memDiff.arrayBuffers / 1024 / 1024 * 100) / 100,
        };

        const currentMemMB = {
            rss: Math.round(memAfter.rss / 1024 / 1024),
            heapTotal: Math.round(memAfter.heapTotal / 1024 / 1024),
            heapUsed: Math.round(memAfter.heapUsed / 1024 / 1024),
            external: Math.round(memAfter.external / 1024 / 1024),
            arrayBuffers: Math.round(memAfter.arrayBuffers / 1024 / 1024),
        };

        // 检查是否有显著的内存增长
        const significantHeapIncrease = memDiffMB.heapUsed > 5; // 超过5MB
        const significantRSSIncrease = memDiffMB.rss > 10; // 超过10MB

        if (significantHeapIncrease || significantRSSIncrease) {
            Logger.warn('High memory usage detected', {
                path: req.path,
                method: req.method,
                statusCode: res.statusCode,
                duration,
                memoryIncrease: memDiffMB,
                currentMemory: currentMemMB,
            });
        }

        // 对于聊天接口，总是记录内存使用情况
        if (req.path.includes('/chat') || req.path.includes('/session')) {
            Logger.info('Chat request memory usage', {
                path: req.path,
                method: req.method,
                statusCode: res.statusCode,
                duration,
                memoryIncrease: memDiffMB,
                currentMemory: currentMemMB,
            });
        }
    });

    next();
}

/**
 * 获取当前内存使用情况
 */
export function getCurrentMemoryUsage(): {
    current: MemoryStats;
    formatted: Record<string, string>;
} {
    const mem = process.memoryUsage();
    
    return {
        current: mem,
        formatted: {
            rss: `${Math.round(mem.rss / 1024 / 1024)}MB`,
            heapTotal: `${Math.round(mem.heapTotal / 1024 / 1024)}MB`,
            heapUsed: `${Math.round(mem.heapUsed / 1024 / 1024)}MB`,
            external: `${Math.round(mem.external / 1024 / 1024)}MB`,
            arrayBuffers: `${Math.round(mem.arrayBuffers / 1024 / 1024)}MB`,
        }
    };
}

/**
 * 强制垃圾回收（仅在开发环境使用）
 */
export function forceGarbageCollection(): void {
    if (global.gc) {
        global.gc();
        Logger.info('Forced garbage collection completed', getCurrentMemoryUsage().formatted);
    } else {
        Logger.warn('Garbage collection not available. Start Node.js with --expose-gc flag.');
    }
}
