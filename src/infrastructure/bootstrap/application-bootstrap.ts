/* eslint-disable no-console */
/**
 * 应用启动器
 * 
 * 负责按正确顺序初始化应用的各个组件，避免循环依赖
 */

import { Express } from 'express';
import { Server } from 'http';
import { configLoader, ApplicationConfig } from '@/infrastructure/config/config-loader';

class ApplicationBootstrap {
    private config: ApplicationConfig | null = null;
    private app: Express | null = null;
    private server: Server | null = null;

    /**
     * 初始化配置
     */
    private async initializeConfiguration(): Promise<void> {
        console.log('🔧 Initializing configuration...');
        this.config = await configLoader.loadConfiguration();
        console.log('✅ Configuration initialized');
    }

    /**
     * 初始化日志系统
     */
    private async initializeLogger(): Promise<void> {
        console.log('📝 Initializing logger...');

        // 动态导入Logger以确保配置已经加载
        const { Logger } = await import('@/infrastructure/logger/winston-logger');

        Logger.info('Logger initialized with updated configuration', {
            logLevel: this.config?.LOG_LEVEL,
            logDir: this.config?.LOG_DIR,
            format: this.config?.LOG_FORMAT
        });

        // 启动智能错误日志记录器的清理机制
        const { SmartErrorLogger } = await import('@/infrastructure/logger/smart-error-logger');
        SmartErrorLogger.startPeriodicCleanup();

        console.log('✅ Logger initialized');
    }

    private async initializeLangfuse(): Promise<void> {
        console.log('🔍 Initializing Langfuse...');
        const { Logger } = await import('@/infrastructure/logger/winston-logger');
        const { getLangfuseConfig } = await import('@/infrastructure/config');
        const langfuseConfig = getLangfuseConfig();
        Logger.info('Langfuse configuration loaded', {
            enabled: langfuseConfig['enabled'],
            baseUrl: langfuseConfig['baseUrl']
        });

        // 动态导入Langfuse服务以确保配置已经加载
        const { getLangfuseService } = await import('@/infrastructure/logger/langfuse');
        const langfuseService = getLangfuseService();

        if (langfuseService.isEnabled()) {
            Logger.info('Langfuse initialized');
        } else {
            Logger.info(' Langfuse not enabled');
        }
    }

    /**
     * 初始化数据库连接
     */
    private async initializeDatabase(): Promise<void> {
        console.log('🗄️  Initializing database connection...');

        if (!this.config) {
            throw new Error('Configuration must be initialized before database');
        }

        try {
            // 动态导入数据库服务以确保日志系统已经初始化
            const { databaseService } = await import('@/infrastructure/database');
            await databaseService.ensureInitialized();
            console.log('✅ Database connection initialized');
        } catch (error) {
            console.error('❌ Failed to initialize database connection:', error);
            throw error;
        }
    }

    /**
     * 初始化依赖注入容器
     */
    private async initializeDependencyContainer(): Promise<void> {
        console.log('📦 Initializing dependency injection container...');

        try {
            // 动态导入容器配置以确保所有依赖都已准备好
            const { ContainerConfig } = await import('@/app/container');

            // 显式注册服务（确保配置已加载）
            ContainerConfig.registerServices();

            // 初始化容器
            await ContainerConfig.initialize();

            console.log('✅ Dependency injection container initialized');
        } catch (error) {
            console.error('❌ Failed to initialize dependency container:', error);
            throw error;
        }
    }

    /**
     * 创建 Express 应用
     */
    private async createExpressApp(): Promise<void> {
        const { Logger } = await import('@/infrastructure/logger/winston-logger');
        const { createApp } = await import('@/app/app');
        Logger.info('Creating Express application...');
        
        if (!this.config) {
            Logger.error('Configuration not initialized before creating app');
            throw new Error('Configuration must be initialized before creating app');
        }

        this.app = createApp();
        Logger.info('Express application created successfully');
    }

    /**
     * 配置路由
     */
    private async configureApplicationRoutes(): Promise<void> {
        const { Logger } = await import('@/infrastructure/logger/winston-logger');
        const { configureRoutes } = await import('@/app/app');
        if (!this.app) {
            throw new Error('Express app must be created before configuring routes');
        }

        try {
            await configureRoutes(this.app);
            Logger.info('Application routes configured successfully');
        } catch (error) {
            Logger.error('Failed to configure application routes', {}, error as Error);
            throw error;
        }
    }

    /**
     * 启动 HTTP 服务器
     */
    private async startHttpServer(): Promise<void> {
        const { Logger } = await import('@/infrastructure/logger/winston-logger');

        return new Promise((resolve, reject) => {
            if (!this.app || !this.config) {
                reject(new Error('App and configuration must be initialized before starting server'));
                return;
            }

            Logger.info('Starting HTTP server...');

            const host = this.config.HOST
            const port = this.config.PORT;
            
            this.server = this.app.listen(port, host, () => {
                Logger.info('Server started successfully', {
                    port,
                    host,
                });

                resolve();
            });

            this.server.on('error', (error: Error & { code?: string }) => {
                if (error.code === 'EADDRINUSE') {
                    Logger.error(`Port ${port} is already in use`);
                } else {
                    Logger.error('Server startup error:', {}, error);
                }
                reject(error);
            });
        });
    }

    /**
     * 设置优雅关闭处理
     */
    private async setupGracefulShutdown(): Promise<void> {
        const { Logger } = await import('@/infrastructure/logger/winston-logger');
        const gracefulShutdown = async (signal: string): Promise<void> => {
            Logger.info(`Received ${signal}, starting graceful shutdown...`);
            
            try {
                // 关闭 HTTP 服务器
                if (this.server) {
                    Logger.info('Closing HTTP server...');
                    await new Promise<void>((resolve) => {
                        this.server!.close(() => {
                            Logger.info('HTTP server closed');
                            resolve();
                        });
                    });
                }

                // 关闭 Langfuse 客户端
                Logger.info('Closing Langfuse client...');
                try {
                    const { getLangfuseService } = await import('@/infrastructure/logger/langfuse');
                    const langfuseService = getLangfuseService();
                    await langfuseService.shutdown();
                } catch (langfuseError) {
                    Logger.warn('Error closing Langfuse client', { error: langfuseError });
                }

                // 关闭数据库连接
                Logger.info('Closing database connection...');
                const { databaseService } = await import('@/infrastructure/database');
                await databaseService.destroy();

                // 关闭配置加载器
                Logger.info('Closing configuration loader...');
                await configLoader.close();
                Logger.info('Configuration loader closed');

                // 清理智能错误日志记录器
                Logger.info('Cleaning up SmartErrorLogger...');
                const { SmartErrorLogger } = await import('@/infrastructure/logger/smart-error-logger');
                SmartErrorLogger.destroy();

                Logger.info('Graceful shutdown completed');
                process.exit(0);
            } catch (error) {
                Logger.error('Error during graceful shutdown:', {}, error as Error);
                process.exit(1);
            }
        };

        // 监听关闭信号
        process.on('SIGTERM', () => void gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => void gracefulShutdown('SIGINT'));

        // 监听未捕获的异常
        process.on('uncaughtException', (error) => {
            Logger.error('Uncaught Exception:', {}, error as Error);
            void gracefulShutdown('uncaughtException');
        });

        process.on('unhandledRejection', (reason, promise) => {
            Logger.error('Unhandled Rejection at:', {promise: String(promise) }, reason as Error);
            void gracefulShutdown('unhandledRejection');
        });
    }

    /**
     * 启动应用
     */
    async start(): Promise<void> {
        try {
            console.log('🚀 Starting XUI App Server...');
            console.log('=' .repeat(50));

            // 1. 初始化配置（包括 Nacos）
            await this.initializeConfiguration();

            // 2. 初始化日志系统
            await this.initializeLogger();

            // 初始化langfuse
            await this.initializeLangfuse();

            // 3. 初始化数据库连接
            await this.initializeDatabase();

            // 4. 初始化依赖注入容器
            await this.initializeDependencyContainer();

            // 5. 创建 Express 应用
            await this.createExpressApp();

            // 6. 配置路由
            await this.configureApplicationRoutes();

            // 7. 设置优雅关闭处理
            await this.setupGracefulShutdown();

            // 8. 启动 HTTP 服务器
            await this.startHttpServer();

            console.log('=' .repeat(50));
            console.log('🎉 XUI App Server started successfully!');

        } catch (error) {
            console.error('❌ Failed to start application:', error);
            
            // 尝试清理资源
            try {
                await this.cleanup();
            } catch (cleanupError) {
                console.error('❌ Error during cleanup:', cleanupError);
            }
            
            process.exit(1);
        }
    }

    /**
     * 清理资源
     */
    private async cleanup(): Promise<void> {
        console.log('🧹 Cleaning up resources...');

        try {
            if (this.server) {
                this.server.close();
            }

            // 关闭Langfuse客户端并刷新所有待处理事件
            try {
                const getLangfuseService = (await import('@/infrastructure/logger/langfuse')).default;
                const langfuse = getLangfuseService();
                await langfuse.shutdown();
                console.log('✅ Langfuse client shutdown completed');
            } catch (langfuseError) {
                console.error('❌ Error shutting down Langfuse:', langfuseError);
            }

            const { databaseService } = await import('@/infrastructure/database');
            await databaseService.destroy();
            await configLoader.close();

            console.log('✅ Cleanup completed');
        } catch (error) {
            console.error('❌ Error during cleanup:', error);
        }
    }

    /**
     * 获取应用实例
     */
    getApp(): Express {
        if (!this.app) {
            throw new Error('Application not started. Call start() first.');
        }
        return this.app;
    }

    /**
     * 获取配置
     */
    getConfig(): ApplicationConfig {
        if (!this.config) {
            throw new Error('Configuration not loaded. Call start() first.');
        }
        return this.config;
    }
}

// 导出单例实例
export const applicationBootstrap = new ApplicationBootstrap();
