/* eslint-disable no-console */
/**
 * 基础设施配置
 * 
 * 基础设施层的中央配置管理，支持Nacos配置中心。
 */

import { configLoader } from '@/infrastructure/config/config-loader';

/**
 * 获取数据库配置（支持Nacos配置覆盖）
 */
export function getDatabaseConfig(): Record<string, unknown> {
    const config = configLoader.getConfiguration();
    return {
        host: config.DB_HOST,
        port: config.DB_PORT,
        database: config.DB_NAME,
        user: config.DB_USER,
        password: config.DB_PASSWORD,
        ssl: config.DB_SSL,
        // 连接池配置 (内存优化)
        max: parseInt(String(config['DB_MAX_CONNECTIONS']) || '50', 10),           // 最大连接数（优化：降低到50）
        min: parseInt(String(config['DB_MIN_CONNECTIONS']) || '10', 10),            // 最小连接数（优化：降低到5）
        idleTimeoutMillis: parseInt(String(config['DB_IDLE_TIMEOUT']) || '15000', 10),     // 空闲超时（优化：缩短到15秒）
        connectionTimeoutMillis: parseInt(String(config['DB_CONNECTION_TIMEOUT']) || '5000', 10), // 连接超时
        maxLifetimeMillis: parseInt(String(config['DB_MAX_LIFETIME']) || '1800000', 10),   // 连接最大生命周期（优化：缩短到30分钟）
    } as const;
}

/**
 * 获取服务器配置（支持Nacos配置覆盖）
 */
export function getServerConfig(): Record<string, unknown> {
    const config = configLoader.getConfiguration();
    return {
        port: config.PORT || 3000,
        host: config.HOST || '0.0.0.0',
    } as const;
}

/**
 * 获取CORS配置（支持Nacos配置覆盖）
 */
export function getCorsConfig(): Record<string, unknown> {
    const config = configLoader.getConfiguration();
    return {
        origin: config.CORS_ORIGIN,
        credentials: config.CORS_CREDENTIALS,
    } as const;
}

/**
 * 获取日志配置（支持Nacos配置覆盖）
 */
export function getLoggingConfig(): Record<string, unknown> {
    const config = configLoader.getConfiguration();
    const logDir = config.LOG_DIR;

    // 添加调试信息以验证配置来源
    const loggingConfig = {
        level: config.LOG_LEVEL,
        format: config.LOG_FORMAT,
        combinedLogFile: `${logDir}/xuiappserver.log`, // 统一日志文件
        logDir, // 添加原始 logDir 用于调试
    } as const;

    console.log('Logging config:', {
        logDir,
        level: loggingConfig.level,
        format: loggingConfig.format,
        combinedLogFile: loggingConfig.combinedLogFile,
        source: 'getLoggingConfig()'
    });

    return loggingConfig;
}

/**
 * 获取安全配置（支持Nacos配置覆盖）
 */
export function getSecurityConfig(): Record<string, unknown> {
    const config = configLoader.getConfiguration();
    return {
        bcryptRounds: parseInt(String(config['BCRYPT_ROUNDS']) || '10', 10),
        rateLimitWindowMs: parseInt(String(config['RATE_LIMIT_WINDOW_MS']) || '900000', 10),
        rateLimitMaxRequests: parseInt(String(config['RATE_LIMIT_MAX_REQUESTS']) || '100', 10),
    } as const;
}

/**
 * 获取优雅关闭配置（支持Nacos配置覆盖）
 */
export function getGracefulShutdownConfig(): Record<string, unknown> {
    const config = configLoader.getConfiguration();
    return {
        timeout: parseInt(String(config['GRACEFUL_SHUTDOWN_TIMEOUT'] ?? '30000'), 10),
    } as const;
}

/**
 * 获取Redis配置（支持Nacos配置覆盖）
 */
export function getRedisConfig(): Record<string, unknown> | undefined {
    const config = configLoader.getConfiguration();

    const redisUrl = config['REDIS_URL'];
    return (redisUrl !== undefined && redisUrl !== null) ? {
        url: redisUrl,
        password: config['REDIS_PASSWORD'],
    } : undefined;
}

/**
 * Langfuse配置
 */
export const getLangfuseConfig = (): Record<string, unknown> => {
    const config = configLoader.getConfiguration();
    return {
        publicKey: config['LANGFUSE_PUBLIC_KEY'],
        secretKey: config['LANGFUSE_SECRET_KEY'],
        baseUrl: config['LANGFUSE_HOST'] ?? 'https://langfuse.yxt.com.cn',
        enabled: !!(
            config['LANGFUSE_PUBLIC_KEY'] !== undefined
            && config['LANGFUSE_PUBLIC_KEY'] !== null
            && typeof config['LANGFUSE_PUBLIC_KEY'] === 'string'
            && config['LANGFUSE_PUBLIC_KEY'].trim() !== ''
            && config['LANGFUSE_SECRET_KEY'] !== undefined
            && config['LANGFUSE_SECRET_KEY'] !== null
            && typeof config['LANGFUSE_SECRET_KEY'] === 'string'
            && config['LANGFUSE_SECRET_KEY'].trim() !== ''
        ),
    };
};

/**
 * 获取运行环境配置
 */
export function getRunEnvCongfig(): string | undefined {
    const config = configLoader.getConfiguration();
    return config['RUN_ENV'] as string | undefined;
}